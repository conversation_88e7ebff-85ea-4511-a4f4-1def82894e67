package com.exam.test2;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 配置文件管理器，单例模式
 */
public class ChainConfigManager {
    private static volatile ChainConfigManager instance;
    private List<ProcessorConfig> processorConfigs;

    private ChainConfigManager() {
        loadConfig();
    }

    public static ChainConfigManager getInstance() {
        if (instance == null) {
            synchronized (ChainConfigManager.class) {
                if (instance == null) {
                    instance = new ChainConfigManager();
                }
            }
        }
        return instance;
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("chain_config.xml");
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(inputStream);

            processorConfigs = new ArrayList<>();
            NodeList processorNodes = document.getElementsByTagName("processor");

            for (int i = 0; i < processorNodes.getLength(); i++) {
                Element processorElement = (Element) processorNodes.item(i);
                String name = processorElement.getAttribute("name");
                String className = processorElement.getAttribute("class");
                int sequence = Integer.parseInt(processorElement.getAttribute("sequence"));

                processorConfigs.add(new ProcessorConfig(name, className, sequence));
            }

            // 按序号排序
            processorConfigs.sort(Comparator.comparingInt(ProcessorConfig::getSequence));

        } catch (Exception e) {
            throw new RuntimeException("Failed to load chain config", e);
        }
    }

    public List<ProcessorConfig> getProcessorConfigs() {
        return processorConfigs;
    }

    /**
     * 处理器配置类
     */
    public static class ProcessorConfig {
        private String name;
        private String className;
        private int sequence;

        public ProcessorConfig(String name, String className, int sequence) {
            this.name = name;
            this.className = className;
            this.sequence = sequence;
        }

        public String getName() {
            return name;
        }

        public String getClassName() {
            return className;
        }

        public int getSequence() {
            return sequence;
        }
    }
}
