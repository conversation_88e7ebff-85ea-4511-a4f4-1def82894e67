package com.exam.test2;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * Test2Executor测试类
 */
public class Test2ExecutorTest {
    public static void main(String[] args) {
        try {
            // 创建测试文件
            String testFilePath = createTestFile();
            
            // 执行处理
            Test2Executor executor = new Test2Executor();
            String result = executor.execute(testFilePath);
            
            System.out.println("原始内容包含敏感词，处理后的结果：");
            System.out.println(result);
            
            // 清理测试文件
            new File(testFilePath).delete();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 创建包含敏感词的测试文件
     */
    private static String createTestFile() throws IOException {
        String testFilePath = "test_article.txt";
        FileWriter writer = new FileWriter(testFilePath);
        
        // 写入包含敏感词的测试内容
        writer.write("这是一篇测试文章。\n");
        writer.write("文章中包含一些敏感词，比如打人、拆迁等民生类敏感词。\n");
        writer.write("还包含一些暴恐类敏感词，如法轮功、轮功等。\n");
        writer.write("这些敏感词应该被替换为*号。\n");
        writer.write("正常的文字内容应该保持不变。\n");
        
        writer.close();
        return testFilePath;
    }
}
