package com.exam.test2;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 处理链管理器
 */
public class ProcessingChainManager {
    private List<IDataProcessor> processors;
    private ExecutorService executorService;
    private static final int DEFAULT_THREAD_POOL_SIZE = 5;

    public ProcessingChainManager() {
        this(DEFAULT_THREAD_POOL_SIZE);
    }

    public ProcessingChainManager(int threadPoolSize) {
        this.processors = new ArrayList<>();
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);
        initializeProcessors();
    }

    /**
     * 根据配置文件初始化处理器
     */
    private void initializeProcessors() {
        ChainConfigManager configManager = ChainConfigManager.getInstance();
        List<ChainConfigManager.ProcessorConfig> configs = configManager.getProcessorConfigs();

        for (ChainConfigManager.ProcessorConfig config : configs) {
            try {
                IDataProcessor processor = createProcessor(config.getName());
                if (processor != null) {
                    processors.add(processor);
                }
            } catch (Exception e) {
                throw new RuntimeException("Failed to create processor: " + config.getName(), e);
            }
        }
    }

    /**
     * 根据处理器名称创建处理器实例
     */
    private IDataProcessor createProcessor(String processorName) {
        switch (processorName) {
            case "livelihood":
                return new LivelihoodSensitiveWordProcessor();
            case "fear":
                return new FearSensitiveWordProcessor();
            default:
                throw new IllegalArgumentException("Unknown processor: " + processorName);
        }
    }

    /**
     * 执行处理链
     * @param dataModel 数据模型
     * @return Future对象，用于异步处理
     */
    public Future<DataModel> processAsync(DataModel dataModel) {
        return executorService.submit(() -> {
            process(dataModel);
            return dataModel;
        });
    }

    /**
     * 同步执行处理链
     * @param dataModel 数据模型
     */
    public void process(DataModel dataModel) {
        for (IDataProcessor processor : processors) {
            processor.process(dataModel);
        }
    }

    /**
     * 关闭线程池
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
