package com.exam.test2;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.Set;

/**
 * 暴恐类敏感词处理器
 */
public class FearSensitiveWordProcessor implements IDataProcessor {
    private Set<String> sensitiveWords;

    public FearSensitiveWordProcessor() {
        loadSensitiveWords();
    }

    /**
     * 加载敏感词列表
     */
    private void loadSensitiveWords() {
        sensitiveWords = new HashSet<>();
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("fear_sensitive_words.txt");
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    sensitiveWords.add(line);
                }
            }
            reader.close();
        } catch (Exception e) {
            throw new RuntimeException("Failed to load fear sensitive words", e);
        }
    }

    @Override
    public void process(DataModel dataModel) {
        String content = dataModel.getContent();
        if (content == null) {
            return;
        }

        String processedContent = replaceSensitiveWords(content);
        dataModel.setContent(processedContent);
    }

    /**
     * 替换敏感词为*号
     * @param content 原始内容
     * @return 处理后的内容
     */
    private String replaceSensitiveWords(String content) {
        String result = content;
        for (String sensitiveWord : sensitiveWords) {
            if (result.contains(sensitiveWord)) {
                // 一个字节占一个"*"
                int byteLength = sensitiveWord.getBytes().length;
                StringBuilder replacement = new StringBuilder();
                for (int i = 0; i < byteLength; i++) {
                    replacement.append("*");
                }
                result = result.replace(sensitiveWord, replacement.toString());
            }
        }
        return result;
    }
}
