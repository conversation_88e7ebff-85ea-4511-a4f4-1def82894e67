1.关于平均负载的含义，哪个描述是正确的？
A. CPU 使用率
B. 可运行状态平均进程数
C. 不可中断状态平均进程数
D. 平均活跃进程数


2.执行如下代码，终端输出结果应该是？
final String a = "1", b = "2";
final int c = 1, d = 2;
System.out.println((a + d) == (c + b));
A. true
B. false
C. 24
D. 1212


3.对于 Tomcat 线程，哪个描述是错误的？
A. Java 提供了一些默认的线程池实现，比如 FixedThreadPool 和 CachedThreadPool，它们的本质就是给 ThreadPoolExecutor 设置了不同的参数，是定制版的 ThreadPoolExecutor
B. Tomcat 的线程池也是一个定制版的 ThreadPoolExecutor，Tomcat 重写了 ThreadPoolExecutor 的 execute 方法，定制了自己的任务处理流程
C. Tomcat 线程池的任务队列采用了 Java 中原生的 LinkedBlockingQueue
D. 默认情况下 Tomcat 线程池的任务队列是没有长度限制的，可以通过设置 maxQueueSize 参数来限制任务队列的长度

4.Kafka 中 Producer API 的主要作用是什么？
A. 负责消息传输
B. 负责与 Broker 进行交互
C. 负责提交位移
D. 以上都不正确

5.如果一个程序需要从文件系统里面不断地随机读取数据，文件系统是在传统硬盘上面。当文件很大的情况下（远远超过内存大小），数据读取操作的的期待反应时间大约是多快？
A. 小于 1 毫秒
B. 1 到 3 毫秒
C. 大约 8-10 毫秒左右
D. 20 毫秒以上


6.下列哪个性能优化策略不属于“时间和空间互相转换”？
A. 压缩数据来降低数据的大小，方便网络传输和外部存储
B. 使用系统缓存
C. 使用内容分发网络CDN
D. Copy On Write，写时复制